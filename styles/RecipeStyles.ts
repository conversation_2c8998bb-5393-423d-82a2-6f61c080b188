import { StyleSheet, Platform } from 'react-native';
import { getThemeColors } from '@/styles/Theme';

// Create a function to export styles that can be used with the current theme
const createStyles = (theme: 'light' | 'dark') => {
    const colors = getThemeColors(theme);

    // Add extra padding for the tab bar
    const tabBarHeight = Platform.OS === 'ios' ? 90 : 70;

    return StyleSheet.create({
      container: {
        flex: 1,
        backgroundColor: colors.background,
      },
      loadingContainer: {
        marginTop: tabBarHeight,
      },
      header: {
        backgroundColor: colors.background,
        elevation: 0,
        shadowOpacity: 0,
      },
      title: {
        alignItems: 'center',
      },
      mainScrollContainer: {
        paddingBottom: tabBarHeight,
      },
      categoryScrollContainer: {
        paddingHorizontal: 16,
        paddingTop: 8,
        paddingBottom: 16,
        gap: 12,
      },
      categoryCard: {
        marginRight: 12,
        borderRadius: 25,
        backgroundColor: colors.surface,
        elevation: 2,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      categoryCardContent: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 18,
        gap: 10,
      },
      icon: {
        marginRight: 4,
      },
      categoryCardTitle: {
        fontSize: 16,
        fontWeight: '500',
        color: colors.text,
      },
      recipeCardContainer: {
        position: 'relative',
        marginHorizontal: 16,
        marginTop: 12,
      },
      recipeCard: {
        borderRadius: 16,
        elevation: 2,
        backgroundColor: colors.surface,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      recipeCardContent: {
        borderRadius: 16,
        overflow: 'hidden',
      },
      recipeHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 8,
      },
      recipeTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 4,
        color: colors.text,
      },
      recipeHeaderContent: {
        flex: 1,
        marginRight: 8,
      },
      recipeActionButtons: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: -8,
      },
      recipeInfo: {
        fontSize: 14,
        color: colors.textSecondary,
        marginTop: 4,
      },
      favoriteButton: {
        margin: 0,
      },
      imageContainer: {
        width: '100%',
        height: 300,
        paddingHorizontal: 16,
        paddingBottom: 16,
      },
      recipeImage: {
        width: '100%',
        height: '100%',
        borderRadius: 12,
      },

      expandedImageContainer: {
        width: '100%',
        height: 300,
        paddingHorizontal: 16,
        paddingBottom: 16,
      },
      sectionContainer: {
        paddingHorizontal: 16,
        paddingVertical: 16,
      },
      sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 12,
        color: colors.text,
      },
      servingsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
      },
      servingsLabel: {
        fontSize: 16,
        marginRight: 16,
        color: colors.text,
      },
      servingButton: {
        width: 28,
        height: 28,
        borderWidth: 1,
        borderColor: colors.border,
        borderRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
      },
      servingButtonText: {
        fontSize: 18,
        fontWeight: '500',
        color: colors.text,
      },
      servingsCount: {
        fontSize: 16,
        marginHorizontal: 12,
        color: colors.text,
      },
      ingredientRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 8,
      },
      unavailableIngredientRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: -5,
      },
      ingredientText: {
        fontSize: 16,
        color: colors.text,
        maxWidth: '80%',
      },
      unavailableIngredientText: {
        color: colors.accent,
      },
      addAllButton: {
        marginTop: 16,
        borderColor: colors.border,
        borderRadius: 25,
        backgroundColor: colors.buttonBackground,
      },
      instructionTypesContainer: {
        flexDirection: 'row',
        marginBottom: 16,
        gap: 8,
      },
      instructionTypeChip: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 20,
        backgroundColor: colors.selectionBackground,
      },
      instructionTypeText: {
        fontSize: 14,
        fontWeight: '500',
        color: colors.selectionText,
      },
      instructionsText: {
        fontSize: 16,
        lineHeight: 24,
        color: colors.text,
      },
    });
};

export default createStyles;
