import React, { useState, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import { Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface CaptureViewProps {
    onPhotosTaken: (photos: string[]) => void;
    onClose: () => void;
}

export default function CaptureView({ onPhotosTaken, onClose }: CaptureViewProps) {
    const [permission, requestPermission] = useCameraPermissions();
    const [facing, setFacing] = useState<'back' | 'front'>('back');
    const [photos, setPhotos] = useState<string[]>([]);
    const cameraRef = useRef<any>(null);
    const colorScheme = useColorScheme() || 'light';
    const colors = getThemeColors(colorScheme as 'light' | 'dark');
    const insets = useSafeAreaInsets();

    React.useEffect(() => {
        if (!permission?.granted) {
            requestPermission();
        }
    }, [permission]);

    const takePicture = async () => {
        if (cameraRef.current) {
            try {
                const photo = await cameraRef.current.takePictureAsync({
                    quality: 0.5,
                    base64: true,
                });
                setPhotos([...photos, photo.uri]);
            } catch (error) {
                Alert.alert('Error', 'Failed to take picture');
            }
        }
    };

    const handleDone = () => {
        if (photos.length === 0) {
            Alert.alert('No Photos', 'Please take at least one photo');
            return;
        }
        onPhotosTaken(photos);
    };

    if (!permission) {
        return <View />;
    }
    if (!permission.granted) {
        return <Text>No access to camera</Text>;
    }

    return (
      <View style={[styles.container, { paddingBottom: insets.bottom + 80 }]}>
        <CameraView style={styles.camera} facing={facing} ref={cameraRef} />

        <View style={[styles.buttonContainer, { paddingBottom: Math.max(20, insets.bottom) }]}>
          <TouchableOpacity style={styles.button} onPress={onClose}>
            <MaterialCommunityIcons name='close' size={30} color='white' />
          </TouchableOpacity>

          <TouchableOpacity style={styles.captureButton} onPress={takePicture}>
            <View style={styles.captureButtonInner} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={handleDone}>
            <MaterialCommunityIcons name='check' size={30} color='white' />
          </TouchableOpacity>
        </View>

        {photos.length > 0 && (
          <View style={[styles.photoCount, { top: Math.max(20, insets.top) }]}>
            <Text style={styles.photoCountText}>
              {photos.length} photo{photos.length !== 1 ? 's' : ''} taken
            </Text>
          </View>
        )}
      </View>
    );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    padding: 20,
  },
  button: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
  },
  photoCount: {
    position: 'absolute',
    top: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  photoCountText: {
    color: 'white',
    fontSize: 16,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 8,
    borderRadius: 20,
  },
}); 