export enum MealType {
  BREAKFAST = 'Breakfast',
  LUNCH = 'Lunch',
  DINNER = 'Dinner',
  DESSERT = 'Dessert',
}

export const numRecipesPerMealTypeInitialState = {
  [MealType.BREAKFAST]: 1,
  [MealType.LUNCH]: 1,
  [MealType.DINNER]: 1,
  [MealType.DESSERT]: 1,
};

export type FilterMealType = MealType | 'All';

export enum RecipeSource {
  FOR_YOU = 'For You',
  FROM_INVENTORY = 'From Inventory',
  FAVORITES = 'Favorites',
}

export interface MealTypeItem {
  title: FilterMealType;
  icon: 'food-fork-drink' | 'egg-fried' | 'rice' | 'pot-steam' | 'food-apple' | 'cupcake';
}

export enum InstructionType {
  HIGH_LEVEL = 'High level',
  DETAILED = 'Detailed',
  TEACH_MODE = 'Teach mode',
}

export interface Ingredient {
  name: string;
  available?: boolean; // Optional - will be computed dynamically based on inventory
}

export interface RecipeInstructions {
  [InstructionType.HIGH_LEVEL]: string;
  [InstructionType.DETAILED]: string;
  [InstructionType.TEACH_MODE]: string;
}

export interface Recipe {
  id: string;
  title: string;
  timeInMinutes: number;
  calories: number;
  imageUrl: string;
  ingredients: Ingredient[];
  instructions: RecipeInstructions;
  mealType: MealType;
  compatibleDiets: string[];
}

export interface DietPreference {
  allergy: string[];
  diet: string;
  time: number;
  experience: string;
  calories: number;
  goals: string[];
}

export interface InventoryItem {
  id?: string;
  name: string;
  quantity: number;
  category?: string;
  addedAt?: number;
}

export interface InventoryCategory {
  name: string;
  emoji: string;
  items: InventoryItem[];
}

export enum Tag {
  CageFree = 'Cage-Free',
  DairyFree = 'Dairy-Free',
  FairTrade = 'Fair Trade',
  FreeRange = 'Free Range',
  Fresh = 'Fresh',
  Frozen = 'Frozen',
  GlutenFree = 'Gluten-Free',
  GrassFed = 'Grass-Fed',
  Keto = 'Keto',
  LowFat = 'Low Fat',
  NonGMO = 'Non-GMO',
  NutFree = 'Nut-Free',
  Organic = 'Organic',
  Paleo = 'Paleo',
  SugarFree = 'Sugar-Free',
  WildCaught = 'Wild Caught',
  ZeroFat = '0% Fat',
}

export interface GroceryItem {
  name: string;
  checked: boolean;
  quantity: number;
  tags: Tag[];
}