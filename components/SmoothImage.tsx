import React, { useState, useRef } from 'react';
import { View, Image, Animated, StyleSheet, ImageStyle, ViewStyle } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';

interface SmoothImageProps {
  source: { uri: string };
  style?: ImageStyle;
  containerStyle?: ViewStyle;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  showLoader?: boolean;
  loaderSize?: 'small' | 'large';
  fadeDuration?: number;
  placeholder?: React.ReactNode;
}

const SmoothImage: React.FC<SmoothImageProps> = ({
  source,
  style,
  containerStyle,
  resizeMode = 'cover',
  showLoader = true,
  loaderSize = 'large',
  fadeDuration = 200,
  placeholder,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
    fadeAnim.setValue(0);
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: fadeDuration,
      useNativeDriver: true,
    }).start();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };



  if (hasError) {
    return (
      <View style={[style, styles.errorContainer]}>
        <View style={[styles.errorPlaceholder, { backgroundColor: colors.surface }]} />
      </View>
    );
  }

  return (
    <View style={[style, styles.container]}>
      {/* Background loader */}
      {isLoading && showLoader && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size={loaderSize} color={colors.accent} />
        </View>
      )}

      {/* Main image with fade animation */}
      <Animated.Image
        source={source}
        style={[style, { opacity: fadeAnim }]}
        resizeMode={resizeMode}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    opacity: 0.3,
  },
});

export default SmoothImage;
