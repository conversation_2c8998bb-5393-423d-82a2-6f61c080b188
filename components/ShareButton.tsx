import React from 'react';
import { TouchableOpacity, useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getThemeColors } from '@/styles/Theme';
import { useShare } from '@/hooks/useShare';
import { Recipe, InstructionType, GroceryItem } from '@/components/types';

export enum ShareButtonType {
  RECIPE = 'recipe',
  BASIC_RECIPE = 'basic_recipe',
  GROCERY_LIST = 'grocery_list',
}

interface ShareButtonProps {
  type: ShareButtonType;
  // Recipe sharing props
  recipe?: Recipe;
  instructionType?: InstructionType;
  servings?: number;
  // Grocery list sharing props
  groceryItems?: GroceryItem[];
  // Event handling
  onPress?: (e: any) => void;
  onError?: (error: Error) => void;
  onSuccess?: () => void;
  // Styling
  size?: number;
  color?: string;
  style?: any;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  type,
  recipe,
  instructionType = InstructionType.DETAILED,
  servings = 1,
  groceryItems,
  onPress,
  onError,
  onSuccess,
  size = 24,
  color,
  style,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const { shareRecipe, shareBasicRecipe, shareGroceryList } = useShare();

  const iconColor = color || colors.accent;

  const handleShare = async (e: any) => {
    // Call custom onPress handler if provided
    if (onPress) {
      onPress(e);
    }

    // Prevent event propagation for card components
    if (e?.stopPropagation) {
      e.stopPropagation();
    }

    try {
      switch (type) {
        case ShareButtonType.RECIPE:
          if (!recipe) {
            throw new Error('Recipe is required for recipe sharing');
          }
          await shareRecipe(recipe, instructionType, servings);
          break;

        case ShareButtonType.BASIC_RECIPE:
          if (!recipe) {
            throw new Error('Recipe is required for basic recipe sharing');
          }
          await shareBasicRecipe(recipe);
          break;

        case ShareButtonType.GROCERY_LIST:
          if (!groceryItems) {
            throw new Error('Grocery items are required for grocery list sharing');
          }
          await shareGroceryList(groceryItems);
          break;

        default:
          throw new Error(`Unsupported share type: ${type}`);
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error sharing:', error);
      
      // Call error callback if provided
      if (onError && error instanceof Error) {
        onError(error);
      }
    }
  };

  return (
    <TouchableOpacity onPress={handleShare} style={style}>
      <Ionicons
        name="share-outline"
        color={iconColor}
        size={size}
      />
    </TouchableOpacity>
  );
};

export default ShareButton;
