const feedbackCategories = ['general', 'feature_request', 'bug_report', 'user_experience', 'recipe_quality', 'other'];

/**
 * JSON schema for user feedback extraction and storage
 * Used when extracting feedback from user input and storing as individual documents.
 */
export const singleUserFeedbackSchema = {
  type: 'object',
  properties: {
    feedback: {
      type: 'string',
      description: 'The user feedback about the app, features, or experience',
    },
    category: {
      type: 'string',
      enum: feedbackCategories,
      description: 'Category of the feedback',
    },
  },
  required: ['feedback', 'category'],
  additionalProperties: false,
};
