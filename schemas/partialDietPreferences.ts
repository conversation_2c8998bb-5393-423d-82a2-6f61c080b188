/**
 * JSON schema for partial diet preferences updates
 * 
 * This schema defines the expected structure when users want to modify
 * only specific fields of their dietary preferences. All fields are optional
 * so the LLM only returns the fields that need to be changed.
 */
export const partialDietPreferencesSchema = {
  type: 'object',
  properties: {
    allergies: {
      type: 'array',
      items: { type: 'string' },
    },
    diet: { type: 'string' },
    timeToCook: { type: 'string' },
    experience: { type: 'string' },
    calories: { type: 'number' },
    goals: { type: 'string' },
    notes: { type: 'string' },
  },
  additionalProperties: false,
  // No required fields - all are optional for partial updates
};
