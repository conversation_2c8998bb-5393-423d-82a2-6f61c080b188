import { useChatExperience } from '@/hooks/useChatExperience';
import ChatUI from '@/components/ChatUI';

export default function ChatScreen() {
  const { conversation, messageInput, setMessageInput, isTyping, handleSend } = useChatExperience();

  return (
    <ChatUI
      messages={conversation}
      inputValue={messageInput}
      onInputChange={setMessageInput}
      onSendMessage={handleSend}
      showTypingIndicator={isTyping}
      placeholder='Type a message...'
    />
  );
}